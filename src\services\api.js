// API Configuration
const API_BASE_URL = "http://13.214.188.83:3000";

// Helper function to get auth token
const getAuthToken = () => {
  return localStorage.getItem("authToken");
};

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    "Content-Type": "application/json",
    ...(token && { Authorization: `Bearer ${token}` }),
  };
};

// API Service functions
export const apiService = {
  // Register user
  register: async (userData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Registration failed");
      }

      return data;
    } catch (error) {
      console.error("API Register Error:", error);
      throw error;
    }
  },

  // Login user
  login: async (credentials) => {
    try {
      const response = await fetch(`${API_BASE_URL}/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Login failed");
      }

      return data;
    } catch (error) {
      console.error("API Login Error:", error);
      throw error;
    }
  },

  // Logout user
  logout: () => {
    localStorage.removeItem("authToken");
    localStorage.removeItem("user");
    console.log("User logged out, token and user data cleared");
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    const token = getAuthToken();
    return !!token;
  },

  // Get current user data
  getCurrentUser: () => {
    const userData = localStorage.getItem("user");
    return userData ? JSON.parse(userData) : null;
  },

  // Example of authenticated request (you can use this pattern for other API calls)
  getProfile: async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/profile`, {
        method: "GET",
        headers: getAuthHeaders(),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || "Failed to fetch profile");
      }

      return data;
    } catch (error) {
      console.error("API Profile Error:", error);
      throw error;
    }
  },

  // Create booking
  createBooking: async (bookingData) => {
    try {
      console.log("Mengirim data booking:", bookingData);
      console.log("URL API:", `${API_BASE_URL}/booking`);
      console.log("Headers:", getAuthHeaders());

      const response = await fetch(`${API_BASE_URL}/booking`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify(bookingData),
      });

      console.log("Response status:", response.status);
      console.log("Response headers:", response.headers);

      const data = await response.json();
      console.log("Response data:", data);

      if (!response.ok) {
        throw new Error(
          data.message ||
            `HTTP Error: ${response.status} - ${response.statusText}`
        );
      }

      return data;
    } catch (error) {
      console.error("API Booking Error:", error);
      if (error.name === "TypeError" && error.message.includes("fetch")) {
        throw new Error(
          "Gagal terhubung ke server. Periksa koneksi internet Anda."
        );
      }
      throw new Error(
        error.message || "Gagal membuat booking. Silakan coba lagi."
      );
    }
  },
};

// Helper function to format register data according to backend requirements
export const formatRegisterData = (formData) => {
  return {
    email: formData.email,
    emailVisibility: true,
    password: formData.password,
    passwordConfirm: formData.password,
    name: formData.name,
    verified: false,
  };
};

// Helper function to format login data according to backend requirements
export const formatLoginData = (formData) => {
  return {
    email: formData.email,
    password: formData.password,
  };
};

// Helper function to format booking data according to backend requirements
export const formatBookingData = (personalData, selectedPackages = []) => {
  // Get current user data from localStorage
  const currentUser = apiService.getCurrentUser();
  const userId = currentUser?.id || "user123456789"; // Fallback if no user ID

  console.log("Data personal yang akan diformat:", personalData);
  console.log("User saat ini:", currentUser);

  // Format tanggal dari MM/DD/YYYY ke YYYY-MM-DD jika diperlukan
  let formattedDate = personalData.activityDate || "";
  if (formattedDate && formattedDate.includes("/")) {
    const [month, day, year] = formattedDate.split("/");
    formattedDate = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
  }

  const formattedData = {
    name: personalData.name || "",
    nomortlp: personalData.phone || "",
    tanggal_kegiatan: formattedDate,
    hotel: personalData.hotel || "",
    transport: personalData.needTransport ? "Ya" : "Tidak",
    transport_type:
      personalData.needTransport && personalData.transportType
        ? personalData.transportType
        : null,
    nomortlpdriver:
      !personalData.needTransport && personalData.driverPhone
        ? personalData.driverPhone
        : null,
    user_id: userId,
  };

  console.log("Data yang sudah diformat:", formattedData);
  return formattedData;
};
