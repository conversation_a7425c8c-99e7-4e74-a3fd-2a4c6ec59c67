# Booking Integration Example

## Overview
The `data_diri.jsx` component has been successfully integrated with the booking API at `http://13.214.188.83:3000/booking`. This component now handles:

- ✅ User authentication validation
- ✅ Form data collection and validation
- ✅ API integration with proper token authentication
- ✅ Loading states and error handling
- ✅ Success feedback and data passing to parent component

## API Integration Details

### Endpoint
- **URL**: `http://13.214.188.83:3000/booking`
- **Method**: `POST`
- **Authentication**: Bearer token (automatically handled)

### Request Format
The component sends data in the format expected by the backend:

```json
{
  "name": "<PERSON>",
  "nomortlp": "081234567890",
  "tanggal_kegiatan": "2025-08-15",
  "hotel": "Hotel Ubud",
  "transport": "Ya",
  "transport_type": "Medium Car",
  "user_id": "user123456789"
}
```

### Response Format
Expected response from the backend:

```json
{
  "booking_id": "bk-2025081512345",
  "message": "Booking berhasil dibuat",
  "data": {
    "collectionId": "booking_collection",
    "created": "2025-08-15 14:30:00",
    "id": "bk-2025081512345",
    "name": "John Doe",
    "nomortlp": "081234567890",
    "tanggal_kegiatan": "2025-08-15",
    "hotel": "Hotel Ubud",
    "transport": "Ya",
    "transport_type": "Medium Car",
    "user_id": "user123456789"
  }
}
```

## Usage Example

```jsx
import React, { useState } from 'react';
import DataDiri from './pages/data_diri.jsx';

function BookingPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [bookingResult, setBookingResult] = useState(null);

  const handleBookingSubmit = (result) => {
    console.log('Booking submitted:', result);
    setBookingResult(result);
    // Handle successful booking
    // result.personalData contains the form data
    // result.bookingResponse contains the API response
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  return (
    <div>
      <button onClick={() => setIsModalOpen(true)}>
        Book Now
      </button>
      
      <DataDiri
        isOpen={isModalOpen}
        onSubmit={handleBookingSubmit}
        onClose={handleModalClose}
      />
      
      {bookingResult && (
        <div>
          <h3>Booking Successful!</h3>
          <p>Booking ID: {bookingResult.bookingResponse.booking_id}</p>
          <p>Customer: {bookingResult.personalData.name}</p>
        </div>
      )}
    </div>
  );
}

export default BookingPage;
```

## Features

### Authentication Check
- Automatically checks if user is logged in
- Shows warning message if not authenticated
- Disables booking button for unauthenticated users

### Form Validation
- All required fields must be filled
- Transport selection validation:
  - If "Ya" (Yes): Transport type must be selected
  - If "Tidak" (No): Driver phone number must be provided

### Loading States
- Shows loading spinner during API call
- Disables form during submission
- Prevents multiple submissions

### Error Handling
- Network errors
- API response errors
- Validation errors
- User-friendly error messages

### Success Handling
- Shows success alert
- Calls parent `onSubmit` callback with data
- Automatically closes modal

## Testing

The component includes comprehensive tests covering:
- ✅ Form rendering and validation
- ✅ API integration (success and error cases)
- ✅ Loading states
- ✅ Authentication checks
- ✅ User interactions

Run tests with:
```bash
npm test -- src/test/data_diri.test.jsx
```

## API Service Functions

### New Functions Added

#### `apiService.createBooking(bookingData)`
- Creates a new booking via API
- Automatically includes authentication headers
- Returns booking response data

#### `formatBookingData(personalData)`
- Formats form data for API submission
- Maps frontend field names to backend requirements
- Includes current user ID from authentication

## Security Features

- ✅ Token-based authentication
- ✅ Automatic token inclusion in requests
- ✅ User ID validation
- ✅ Input validation and sanitization

## Next Steps

1. **Error Handling Enhancement**: Add more specific error messages
2. **Offline Support**: Handle network connectivity issues
3. **Data Persistence**: Save draft data locally
4. **Validation Enhancement**: Add phone number format validation
5. **UI/UX Improvements**: Better loading animations and feedback
