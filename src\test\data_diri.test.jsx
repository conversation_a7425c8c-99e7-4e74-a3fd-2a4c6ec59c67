import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import { beforeEach, describe, expect, vi } from "vitest";
import DataDiri from "../pages/data_diri.jsx";
import { apiService } from "../services/api.js";

// Mock the API service
vi.mock("../services/api.js", () => ({
  apiService: {
    createBooking: vi.fn(),
    getCurrentUser: vi.fn(),
  },
  formatBookingData: vi.fn((personalData) => ({
    name: personalData.name,
    nomortlp: personalData.phone,
    tanggal_kegiatan: personalData.activityDate,
    hotel: personalData.hotel,
    transport: personalData.needTransport ? "Ya" : "No",
    transport_type: personalData.needTransport
      ? personalData.transportType
      : "Medium Car",
    user_id: "user123456789",
  })),
}));

// Mock the useAuth hook
vi.mock("../hooks/useAuth.js", () => ({
  useAuth: () => ({
    isAuthenticated: true,
    user: { id: "user123", name: "Test User" },
    token: "test-token",
  }),
}));

// Mock window.alert
global.alert = vi.fn();

describe("DataDiri Component with Booking Integration", () => {
  const mockOnSubmit = vi.fn();
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    apiService.getCurrentUser.mockReturnValue({
      id: "user123",
      name: "Test User",
    });
  });

  it("renders data diri form when open", () => {
    render(
      <DataDiri isOpen={true} onSubmit={mockOnSubmit} onClose={mockOnClose} />
    );

    expect(screen.getByText("Data Diri Peserta")).toBeInTheDocument();
    expect(screen.getByLabelText(/Nama Lengkap/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Nomor Telepon/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Tanggal Kegiatan/)).toBeInTheDocument();
    expect(screen.getByLabelText(/Hotel/)).toBeInTheDocument();
  });

  it("does not render when closed", () => {
    render(
      <DataDiri isOpen={false} onSubmit={mockOnSubmit} onClose={mockOnClose} />
    );

    expect(screen.queryByText("Data Diri Peserta")).not.toBeInTheDocument();
  });

  it("shows validation error for incomplete data", async () => {
    render(
      <DataDiri isOpen={true} onSubmit={mockOnSubmit} onClose={mockOnClose} />
    );

    const submitButton = screen.getByText("Next");
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(
        screen.getByText(/Mohon lengkapi semua data yang diperlukan/)
      ).toBeInTheDocument();
    });
  });

  it("successfully submits booking with complete data", async () => {
    const mockBookingResponse = {
      booking_id: "booking123",
      message: "Booking berhasil dibuat",
    };

    apiService.createBooking.mockResolvedValue(mockBookingResponse);

    render(
      <DataDiri isOpen={true} onSubmit={mockOnSubmit} onClose={mockOnClose} />
    );

    // Fill in the form
    fireEvent.change(screen.getByLabelText(/Nama Lengkap/), {
      target: { value: "John Doe" },
    });
    fireEvent.change(screen.getByLabelText(/Nomor Telepon/), {
      target: { value: "081234567890" },
    });
    fireEvent.change(screen.getByLabelText(/Tanggal Kegiatan/), {
      target: { value: "2025-08-15" },
    });
    fireEvent.change(screen.getByLabelText(/Hotel/), {
      target: { value: "Test Hotel" },
    });

    // Select transport option - No (easier to test)
    fireEvent.click(screen.getByText("Tidak"));

    // Fill driver phone (required when transport = No)
    fireEvent.change(screen.getByLabelText(/Nomor Telepon Driver/), {
      target: { value: "081987654321" },
    });

    // Submit the form
    const submitButton = screen.getByText("Next");
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(apiService.createBooking).toHaveBeenCalled();
      expect(mockOnSubmit).toHaveBeenCalledWith({
        personalData: expect.objectContaining({
          name: "John Doe",
          phone: "081234567890",
          activityDate: "2025-08-15",
          hotel: "Test Hotel",
          needTransport: false,
          driverPhone: "081987654321",
        }),
        bookingResponse: mockBookingResponse,
      });
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it("handles booking API error", async () => {
    const errorMessage = "Booking failed";
    apiService.createBooking.mockRejectedValue(new Error(errorMessage));

    render(
      <DataDiri isOpen={true} onSubmit={mockOnSubmit} onClose={mockOnClose} />
    );

    // Fill in the form
    fireEvent.change(screen.getByLabelText(/Nama Lengkap/), {
      target: { value: "John Doe" },
    });
    fireEvent.change(screen.getByLabelText(/Nomor Telepon/), {
      target: { value: "081234567890" },
    });
    fireEvent.change(screen.getByLabelText(/Tanggal Kegiatan/), {
      target: { value: "2025-08-15" },
    });
    fireEvent.change(screen.getByLabelText(/Hotel/), {
      target: { value: "Test Hotel" },
    });

    // Select transport option - No (easier to test)
    fireEvent.click(screen.getByText("Tidak"));

    // Fill driver phone (required when transport = No)
    fireEvent.change(screen.getByLabelText(/Nomor Telepon Driver/), {
      target: { value: "081987654321" },
    });

    // Submit the form
    const submitButton = screen.getByText("Next");
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
      expect(mockOnSubmit).not.toHaveBeenCalled();
      expect(mockOnClose).not.toHaveBeenCalled();
    });
  });

  it("shows loading state during booking submission", async () => {
    // Mock a delayed response
    apiService.createBooking.mockImplementation(
      () =>
        new Promise((resolve) =>
          setTimeout(() => resolve({ booking_id: "test" }), 100)
        )
    );

    render(
      <DataDiri isOpen={true} onSubmit={mockOnSubmit} onClose={mockOnClose} />
    );

    // Fill in the form
    fireEvent.change(screen.getByLabelText(/Nama Lengkap/), {
      target: { value: "John Doe" },
    });
    fireEvent.change(screen.getByLabelText(/Nomor Telepon/), {
      target: { value: "081234567890" },
    });
    fireEvent.change(screen.getByLabelText(/Tanggal Kegiatan/), {
      target: { value: "2025-08-15" },
    });
    fireEvent.change(screen.getByLabelText(/Hotel/), {
      target: { value: "Test Hotel" },
    });

    // Select transport option - No (easier to test)
    fireEvent.click(screen.getByText("Tidak"));

    // Fill driver phone (required when transport = No)
    fireEvent.change(screen.getByLabelText(/Nomor Telepon Driver/), {
      target: { value: "081987654321" },
    });

    // Submit the form
    const submitButton = screen.getByText("Next");
    fireEvent.click(submitButton);

    // Check loading state
    expect(screen.getByText("Memproses...")).toBeInTheDocument();
    expect(submitButton).toBeDisabled();

    // Wait for completion
    await waitFor(() => {
      expect(screen.queryByText("Memproses...")).not.toBeInTheDocument();
    });
  });
});
